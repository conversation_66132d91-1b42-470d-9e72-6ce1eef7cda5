<!DOCTYPE html>
<html>
<head>
    <title>Test Form Submission</title>
</head>
<body>
    <h1>Test Form Submission</h1>
    
    <form id="test-form" onsubmit="handleSubmit(event)">
        <input type="text" name="test" value="test value" />
        <button type="submit">Submit via type=submit</button>
    </form>
    
    <br><br>
    
    <button onclick="submitFormManually()">Submit via JavaScript</button>
    
    <script>
        function handleSubmit(event) {
            event.preventDefault();
            console.log("🔥 Form submitted!", event);
            console.log("🔥 Form data:", new FormData(event.target));
        }
        
        function submitFormManually() {
            console.log("🔥 Manual submit button clicked");
            const form = document.getElementById('test-form');
            console.log("🔥 Form found:", form);
            if (form) {
                form.requestSubmit();
            }
        }
    </script>
</body>
</html>
