import { useState } from "react";

import { ApolloError } from "@apollo/client";
import { useForm } from "react-hook-form";

import {
  useGetPortalHospitalUploadFileUrlsLazyQuery,
  useUpdatePortalHospitalMutation,
} from "@/apis/gql/operations/__generated__/portal-hospital";
import {
  getPortalHospitalUploadFileUrls,
  mergeBusinessTimes,
} from "@/features/setting-portal/utils";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { logger } from "@/utils/sentry-logger";

import type {
  ImportHospitalData,
  UpdatePortalHospitalInput,
} from "@/apis/gql/generated/types";
import type { GetPortalHospitalQuery } from "@/apis/gql/operations/__generated__/portal-hospital";
import type { UploadFile } from "antd";

type Hospital = NonNullable<
  GetPortalHospitalQuery["getPortalHospitalById"]["portalHospital"]
>;

export type FormData = Omit<
  NonNullable<UpdatePortalHospitalInput["portalHospitalInput"]>,
  "hospitalStations"
> & {
  businessTimes: Hospital["businessTimes"];
  hospitalStations: Hospital["hospitalStations"];
  files: UploadFile[];
};

export const useUpdateHospitalInfo = (hospital: Hospital) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { notification } = useGlobalNotification();
  const { handleError } = useErrorHandler();

  const {
    register,
    control,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    setValue,
    clearErrors,
    watch,
    reset,
    getValues,
  } = useForm<FormData>({
    mode: "onChange",
    defaultValues: {
      ...hospital,
      // FormData型にコンバート
      tagIds: hospital.tags.map((tag) => tag.tagId),
      examinationIds: hospital.examinations.map((exam) => exam.examinationId),
      specialistIds: hospital.specialists.map(
        (specialist) => specialist.specialistId,
      ),
      // UploadFile型にコンバートする
      files: (hospital.files ?? []).map(
        ({ fileId, originalFileName, s3Key }) => ({
          uid: fileId?.toString() ?? "",
          name: originalFileName,
          url: s3Key,
        }),
      ),
    },
  });

  const currentPostcode = watch("postCode");
  const currentAddress1 = watch("address1");

  const handleResetFormAddress1 = (address1: string) => {
    setValue("address1", address1);
  };

  const handleResetFormAddress2 = (address2: string) => {
    setValue("address2", address2);
  };

  const [updatePortalHospital] = useUpdatePortalHospitalMutation();
  const [getUploadFileUrls] = useGetPortalHospitalUploadFileUrlsLazyQuery();

  const submit = async (input: FormData) => {
    console.log(
      "🔥 useUpdateHospitalInfo: submit function called with input:",
      input,
    );
    setIsSubmitting(true);

    try {
      // 更新前の診療時間ID
      const prevBusinessTimeIds = hospital.businessTimes.map(
        (time) => time.businessTimeId,
      );

      // 入力されている診療時間ID
      const inputBusinessTimeIds = input.businessTimes.map(
        (time) => time.businessTimeId,
      );

      // 追加された診療時間
      const addedBusinessTimes = input.businessTimes
        .filter((time) => !prevBusinessTimeIds.includes(time.businessTimeId))
        .map((time) => {
          // 更新に不要なプロパティを削除する
          const timeForUpdate = Object.assign(time);
          delete timeForUpdate["key"];
          delete timeForUpdate["businessTimeId"];
          return timeForUpdate;
        });

      // 削除された診療時間
      const deletedBusinessTimeIds = hospital.businessTimes
        .filter((time) => !inputBusinessTimeIds.includes(time.businessTimeId))
        .map((time) => time.businessTimeId);

      // 既存の診療時間
      const editedBusinessTimes = input.businessTimes.filter((time) =>
        prevBusinessTimeIds.includes(time.businessTimeId),
      );

      // 既存のファイルID群
      const currentFileIds = hospital.files?.length
        ? hospital.files.map((file) => file.fileId?.toString() ?? "")
        : [];

      // 新規で追加する紹介画像
      const addFiles = input.files.filter(
        (file) => !currentFileIds.includes(file.uid),
      );

      // 既存から削除される紹介画像ID群
      const deletedFileIds = currentFileIds
        .filter(
          (fileId) => !input.files.map((file) => file.uid).includes(fileId),
        )
        .map((numberStr) => Number(numberStr));

      // 新規で追加された紹介画像
      let addedFiles = [];

      addedFiles = await getPortalHospitalUploadFileUrls(
        addFiles,
        getUploadFileUrls,
      );

      await updatePortalHospital({
        variables: {
          input: {
            addedBusinessTimes,
            deletedBusinessTimeIds,
            editedBusinessTimes,
            addedFiles,
            deletedFileIds,
            portalHospitalInput: {
              name: input.name,
              postCode: input.postCode,
              address1: input.address1,
              address2: input.address2,
              homePage: input.homePage,
              accessDetail: input.accessDetail,
              telephone: input.telephone,
              directorName: input.directorName,
              paymentDetails: input.paymentDetails,
              isActive: input.isActive,
              isCarpark: input.isCarpark,
              carparkDetail: input.carparkDetail,
              timelineDescription: input.timelineDescription,
              holidayDetail: input.holidayDetail,
              tagIds: input.tagIds,
              examinationIds: input.examinationIds,
              specialistIds: input.specialistIds,
              hospitalStations: input.hospitalStations?.map((station) => ({
                stationId: station.stationId,
                walkingMinute: station.walkingMinute,
              })),
              descriptionTitle: input.descriptionTitle,
              description: input.description,
              mailAddress: input.mailAddress,
            },
          },
        },
        refetchQueries: ["getPortalHospital"],
      });

      notification.success({
        message: "GMOクリニック・マップ連携情報を更新しました",
      });
    } catch (error) {
      logger({ error, message: "failed to update portal hospital" });
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({
          error,
          commonMessage: "GMOクリニック・マップ連携情報の更新に失敗しました",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const setPortalInfoFormValues = (input: ImportHospitalData | undefined) => {
    if (!input) return;
    const {
      name,
      address1,
      address2,
      telephone,
      postCode,
      homepage,
      stations,
      descriptionTitle,
      description,
      businessTimes,
      holidayDetail,
      isCarpark,
      carparkDetail,
      examinations,
      specialists,
      tags,
      payments,
      directorName,
    } = input;

    const formValues: Omit<FormData, "hospitalStations"> & {
      hospitalStations: { stationId: number; stationName: string }[];
    } = {
      isActive: false,
      directorName,
      name: name || "",
      postCode: postCode?.replace("-", "") || "",
      address1: address1 || "",
      address2: address2,
      telephone: telephone || "",
      homePage: homepage,
      hospitalStations:
        stations?.map((item) => ({
          stationId: Number(item.stationId),
          stationName: item.stationDetail?.name?.replace("駅", "") || "",
        })) || [],
      accessDetail: stations
        ?.map((item) => item.stationDetail?.description)
        .join("/n"),
      descriptionTitle: descriptionTitle || "",
      description,
      businessTimes: mergeBusinessTimes(businessTimes || []),
      businessTimeDetail: "",
      holidayDetail,
      isCarpark: isCarpark || false,
      carparkDetail,
      examinationIds: examinations?.map((item) => item.examinationId) || [],
      specialistIds: specialists?.map((item) => item.specialistId),
      tagIds: tags?.map((item) => item.tagId),
      paymentDetails:
        payments?.map((item) => item.paymentTypeName).join("、") || "現金",
      files: [],
    };

    reset(formValues);
  };

  const wrappedOnSubmit = handleSubmit((data) => {
    console.log(
      "🔥 useUpdateHospitalInfo: handleSubmit wrapper called with data:",
      data,
    );
    return submit(data);
  });

  console.log(
    "🔥 useUpdateHospitalInfo: returning onSubmit handler:",
    wrappedOnSubmit,
  );

  return {
    register,
    control,
    submitting: isSubmitting,
    onSubmit: wrappedOnSubmit,
    errors,
    clearErrors,
    handleResetFormAddress1,
    handleResetFormAddress2,
    currentPostcode,
    currentAddress1,
    setPortalInfoFormValues,
    isDirty,
    getValues,
    isValid,
  };
};
