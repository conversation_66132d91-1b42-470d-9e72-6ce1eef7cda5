import { useState } from "react";

import { ApolloError } from "@apollo/client";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";

import { useGetPortalHospitalStaffUploadFileUrlsLazyQuery } from "@/apis/gql/operations/__generated__/portal-hospital";
import { useEditPortalStaffMutation } from "@/apis/gql/operations/__generated__/portal-staff";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { logger } from "@/utils/sentry-logger";

import { getPortalHospitalStaffUploadFileUrls } from "../utils";

import type { GetPortalHospitalStaffQuery } from "@/apis/gql/operations/__generated__/portal-staff";
import type { UploadFile } from "antd";
import type { EditPortalHospitalStaffInput } from "@/apis/gql/generated/types";

type FormType = {
  name: NonNullable<EditPortalHospitalStaffInput["hospitalStaffInput"]>["name"];
  description: NonNullable<
    EditPortalHospitalStaffInput["hospitalStaffInput"]
  >["description"];
  files: UploadFile[];
  specialistDetail: NonNullable<
    EditPortalHospitalStaffInput["hospitalStaffInput"]
  >["specialistDetail"];
  isDirector: NonNullable<
    EditPortalHospitalStaffInput["hospitalStaffInput"]
  >["isDirector"];
  experienceDetail: NonNullable<
    EditPortalHospitalStaffInput["hospitalStaffInput"]
  >["experienceDetail"];
};

export const usePortalStaffEditForm = (
  staffInfo: NonNullable<
    GetPortalHospitalStaffQuery["getPortalHospitalStaff"]["portalHospitalStaff"]
  >,
) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { push } = useRouter();
  const { notification } = useGlobalNotification();
  const { handleError } = useErrorHandler();
  const [editPortalStaff] = useEditPortalStaffMutation();
  const [getUploadFileUrls] =
    useGetPortalHospitalStaffUploadFileUrlsLazyQuery();
  const [isAgreementOpen, setIsAgreementOpen] = useState(false);

  const handleAgreementOpen = () => {
    setIsAgreementOpen(true);
  };

  const handleAgreementClose = () => {
    setIsAgreementOpen(false);
  };
  const {
    register,
    control,
    handleSubmit,
    formState: { errors, isDirty, isValid },
    getValues,
  } = useForm<FormType>({
    defaultValues: {
      files: staffInfo.files?.map((file) => ({
        uid: file.fileId ? file.fileId.toString() : "",
        name: file.originalFileName,
        url: file.s3Key,
      })),
      name: staffInfo.name,
      description: staffInfo.description,
      isDirector: staffInfo.isDirector,
      specialistDetail: staffInfo.specialistDetail,
      experienceDetail: staffInfo.experienceDetail,
    },
  });

  const submit = async (input: FormType) => {
    setIsSubmitting(true);
    console.log("input", input);
    try {
      /** ファイル */
      const originalFileIds =
        staffInfo.files?.map((file) => file.fileId?.toString() ?? "") ?? [];

      const inputFileIds = input.files.map((file) => file.uid);

      const addFiles = input.files.filter(
        (file) => !originalFileIds.includes(file.uid),
      );

      const addedFiles = addFiles
        ? await getPortalHospitalStaffUploadFileUrls(
            staffInfo.hospitalStaffId,
            addFiles,
            getUploadFileUrls,
          )
        : [];

      const deletedFileIds = originalFileIds
        .filter((fileId) => !inputFileIds.includes(fileId))
        .map((fileId) => Number(fileId));

      await editPortalStaff({
        variables: {
          input: {
            hospitalStaffId: staffInfo.hospitalStaffId,
            hospitalStaffInput: {
              name: input.name,
              description: input.description,
              specialistDetail: input.specialistDetail,
              isDirector: input.isDirector,
              experienceDetail: input.experienceDetail,
            },
            addedFiles,
            deletedFileIds,
          },
        },
        refetchQueries: ["getPortalHospitalStaffs"],
      });

      await push("/setting/portal/staff");
      notification.success({ message: "医師情報を作成しました" });
    } catch (error) {
      logger({
        error,
        message: "failed to create portal staff",
        reference: {
          hospitalStaffId: staffInfo.hospitalStaffId,
        },
      });
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({
          error,
          commonMessage: "医師情報の編集に失敗しました",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    register,
    control,
    onSubmit: handleSubmit(submit),
    errors,
    isAgreementOpen,
    handleAgreementOpen: handleSubmit(handleAgreementOpen),
    handleAgreementClose,
    isSubmitting,
    isDirty,
    getValues,
    isValid,
  };
};
