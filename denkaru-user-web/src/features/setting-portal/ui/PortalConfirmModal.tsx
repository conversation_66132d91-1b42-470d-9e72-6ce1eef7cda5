import { useState, useEffect, useRef } from "react";

import Link from "next/link";
import styled from "styled-components";

import { Checkbox } from "@/components/ui/Checkbox";
import { Modal } from "@/components/ui/Modal";
import { TERMS_AICHART } from "@/constants/links";
import { Button } from "@/components/ui/NewButton";

const TextContent = styled.div`
  font-size: 14px;
  margin: 10px;
`;

const ContentWrapper = styled.div`
  padding: 14px 24px;
  height: 456px;
`;

const ContentBlock = styled.div`
  padding-top: 20px;
  font-weight: bold;
`;

const TextLink = styled(Link)`
  color: #007aff;
`;

const CheckboxBlock = styled.div`
  background-color: #f1f4f7;
  display: flex;
  justify-content: center;
  margin-top: 40px;
  padding: 22px;
`;

type Props = {
  formId: string;
  title: string;
  isOpen: boolean;
  onClose: () => void;
  onCancel?: () => void;
  isSubmitting: boolean;
  onSubmitSuccess?: () => void;
};
export const PortalConfirmModal = ({
  formId,
  title,
  isOpen,
  onClose,
  onCancel,
  isSubmitting,
  onSubmitSuccess,
}: Props) => {
  const [isChecked, setIsChecked] = useState<boolean>(false);

  return (
    <Modal
      isOpen={isOpen}
      title={title}
      width={760}
      centered
      onCancel={onClose}
      footer={[
        <Button
          key="cancel"
          varient="tertiary"
          onClick={() => {
            if (onCancel) {
              onCancel();
            } else {
              onClose();
            }
            setIsChecked(false);
          }}
        >
          キャンセル
        </Button>,
        <Button
          key="submit"
          varient={!isChecked ? "tertiary" : "primary"}
          htmlType="submit"
          form={formId}
          disabled={!isChecked || isSubmitting}
        >
          保存
        </Button>,
      ]}
    >
      <ContentWrapper>
        <TextContent>
          下記の内容をよくご確認頂き、「同意する」にチェックを入れてから「保存」ボタンを押してください。同意をえられない場合は保存することが出来ません。
        </TextContent>
        <TextContent>次の3つの項目を遵守することに同意します。</TextContent>
        <ContentBlock>
          <TextContent>
            1. ページに記載されている情報が全て事実と合致していること
          </TextContent>
          <TextContent>
            2.
            <TextLink
              href="https://www.mhlw.go.jp/stf/seisakunitsuite/bunya/kenkou_iryou/iryou/kokokukisei/index.html"
              target="_blank"
              rel="noopener noreferrer"
            >
              医療広告ガイドライン
            </TextLink>
            をはじめとした諸法令を遵守すること
          </TextContent>
          <TextContent>
            3.
            <TextLink
              href={TERMS_AICHART}
              target="_blank"
              rel="noopener noreferrer"
            >
              AIチャート利用規約
            </TextLink>
            を理解し遵守すること
          </TextContent>
        </ContentBlock>
        <CheckboxBlock>
          <Checkbox
            checked={isChecked}
            onChange={() => setIsChecked(!isChecked)}
          >
            上記項目に同意する
          </Checkbox>
        </CheckboxBlock>
      </ContentWrapper>
    </Modal>
  );
};
