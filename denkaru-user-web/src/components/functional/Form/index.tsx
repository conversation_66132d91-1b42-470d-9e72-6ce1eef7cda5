import { forwardRef } from "react";

interface Props extends React.HTMLProps<HTMLFormElement> {
  children?: React.ReactNode;
}
/**
 * Formコンポーネントはフォーム要素のラッパーです。ユーザーがEnterキーを押すと、フォームの送信を防止します。ただし、textareaは除外されます。
 *
 * Form component is a wrapper for form element. It prevents form submission when user press Enter key.Except textarea.
 */
export const Form = forwardRef<HTMLFormElement, Props>((props, ref) => {
  console.log("🔥 Form component rendered with props:", props);

  return (
    <form
      {...props}
      onSubmit={(event) => {
        console.log("🔥 Form onSubmit called:", event);
        console.log("🔥 Form onSubmit handler:", props.onSubmit);
        if (props.onSubmit) {
          props.onSubmit(event);
        }
      }}
      onKeyDown={(event) => {
        if (
          event.key === "Enter" &&
          (event.target as HTMLElement).nodeName !== "TEXTAREA"
        ) {
          event.preventDefault();
        }
      }}
      ref={ref}
      noValidate
    >
      {props.children}
    </form>
  );
});

Form.displayName = "Form";
